# 多阶段构建：构建阶段
FROM node:24-alpine AS builder

WORKDIR /app

# 配置npm使用国内镜像源（加速下载）
RUN npm config set registry https://registry.npmmirror.com && \
    npm config set disturl https://npmmirror.com/dist && \
    npm config set electron_mirror https://npmmirror.com/mirrors/electron/ && \
    npm config set sass_binary_site https://npmmirror.com/mirrors/node-sass/ && \
    npm config set phantomjs_cdnurl https://npmmirror.com/mirrors/phantomjs/

# 复制package文件和npm配置
COPY package*.json ./
COPY .npmrc ./

# 安装依赖（使用npm ci获得更快、更可靠的构建）
RUN npm ci

# 复制源代码
COPY . .

# 构建生产版本
RUN npm run build

# 生产阶段：使用nginx服务静态文件
FROM nginx:1.29.0-alpine

# 配置Alpine镜像源（如果需要安装额外包）
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories

# 复制构建的文件到nginx
COPY --from=builder /app/build /usr/share/nginx/html

# 复制nginx配置
COPY nginx.conf /etc/nginx/conf.d/default.conf

# 暴露端口
EXPOSE 80

# 启动nginx
CMD ["nginx", "-g", "daemon off;"]